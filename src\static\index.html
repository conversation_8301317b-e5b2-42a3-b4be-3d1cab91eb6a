<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدير Arduino - إدارة عدة أجهزة Arduino</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 40px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border-left: 5px solid #007bff;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        
        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group textarea:focus, .form-group select:focus {
            outline: none;
            border-color: #007bff;
        }
        
        .btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }
        
        .arduino-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-right: 5px solid #28a745;
        }
        
        .arduino-card.offline {
            border-right-color: #dc3545;
        }
        
        .arduino-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .arduino-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .info-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
        }
        
        .info-item strong {
            color: #495057;
        }
        
        .status-online {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-offline {
            color: #dc3545;
            font-weight: bold;
        }
        
        .command-section {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin-top: 15px;
        }
        
        .command-history {
            max-height: 300px;
            overflow-y: auto;
            background: white;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        
        .command-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            margin-bottom: 10px;
        }
        
        .command-item:last-child {
            border-bottom: none;
        }
        
        .command-status {
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-completed {
            background: #d4edda;
            color: #155724;
        }
        
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .broadcast-section {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .broadcast-section h2 {
            color: white;
            margin-bottom: 20px;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .main-content {
                padding: 20px;
            }
            
            .arduino-info {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 مدير Arduino</h1>
            <p>إدارة وتحكم في عدة أجهزة Arduino من مكان واحد</p>
        </div>
        
        <div class="main-content">
            <!-- إضافة Arduino جديد -->
            <div class="section">
                <h2>➕ إضافة Arduino جديد</h2>
                <form id="addArduinoForm">
                    <div class="form-group">
                        <label for="arduinoName">اسم الجهاز:</label>
                        <input type="text" id="arduinoName" name="name" required placeholder="مثال: Arduino غرفة المعيشة">
                    </div>
                    <div class="form-group">
                        <label for="arduinoIP">عنوان IP:</label>
                        <input type="text" id="arduinoIP" name="ip_address" placeholder="*************">
                    </div>
                    <div class="form-group">
                        <label for="arduinoPort">المنفذ:</label>
                        <input type="number" id="arduinoPort" name="port" value="80" placeholder="80">
                    </div>
                    <div class="form-group">
                        <label for="arduinoBoardType">نوع اللوحة:</label>
                        <select id="arduinoBoardType" name="board_type">
                            <option value="">اختر نوع اللوحة</option>
                            <option value="Arduino Uno">Arduino Uno</option>
                            <option value="Arduino Nano">Arduino Nano</option>
                            <option value="Arduino Mega">Arduino Mega</option>
                            <option value="ESP32">ESP32</option>
                            <option value="ESP8266">ESP8266</option>
                            <option value="NodeMCU">NodeMCU</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="arduinoDescription">الوصف:</label>
                        <textarea id="arduinoDescription" name="description" rows="3" placeholder="وصف مختصر للجهاز ووظيفته"></textarea>
                    </div>
                    <button type="submit" class="btn">إضافة Arduino</button>
                </form>
            </div>
            
            <!-- إرسال أمر لجميع الأجهزة -->
            <div class="broadcast-section">
                <h2>📡 إرسال أمر لجميع الأجهزة المتصلة</h2>
                <div class="form-group">
                    <input type="text" id="broadcastCommand" placeholder="أدخل الأمر المراد إرساله لجميع الأجهزة" style="margin-bottom: 15px;">
                    <button onclick="broadcastCommand()" class="btn" style="background: white; color: #ee5a24;">إرسال للجميع</button>
                </div>
            </div>
            
            <!-- قائمة الأجهزة -->
            <div class="section">
                <h2>📱 الأجهزة المسجلة</h2>
                <button onclick="refreshArduinos()" class="btn btn-success">تحديث القائمة</button>
                <button onclick="pingAllArduinos()" class="btn btn-warning">فحص الاتصال للجميع</button>
                
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>جاري التحميل...</p>
                </div>
                
                <div id="arduinosList"></div>
            </div>
        </div>
    </div>

    <script>
        // متغيرات عامة
        let arduinos = [];
        
        // تحميل الأجهزة عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            refreshArduinos();
        });
        
        // إضافة Arduino جديد
        document.getElementById('addArduinoForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());
            
            try {
                const response = await fetch('/api/arduinos', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                if (response.ok) {
                    showAlert('تم إضافة Arduino بنجاح!', 'success');
                    e.target.reset();
                    refreshArduinos();
                } else {
                    const error = await response.json();
                    showAlert('خطأ في إضافة Arduino: ' + error.error, 'error');
                }
            } catch (error) {
                showAlert('خطأ في الاتصال: ' + error.message, 'error');
            }
        });
        
        // تحديث قائمة الأجهزة
        async function refreshArduinos() {
            showLoading(true);
            
            try {
                const response = await fetch('/api/arduinos');
                if (response.ok) {
                    arduinos = await response.json();
                    displayArduinos();
                } else {
                    showAlert('خطأ في تحميل الأجهزة', 'error');
                }
            } catch (error) {
                showAlert('خطأ في الاتصال: ' + error.message, 'error');
            }
            
            showLoading(false);
        }
        
        // عرض الأجهزة
        function displayArduinos() {
            const container = document.getElementById('arduinosList');
            
            if (arduinos.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; padding: 40px;">لا توجد أجهزة مسجلة بعد</p>';
                return;
            }
            
            container.innerHTML = arduinos.map(arduino => `
                <div class="arduino-card ${arduino.status === 'offline' ? 'offline' : ''}">
                    <h3>${arduino.name}</h3>
                    <div class="arduino-info">
                        <div class="info-item">
                            <strong>الحالة:</strong> 
                            <span class="status-${arduino.status}">${arduino.status === 'online' ? 'متصل' : 'غير متصل'}</span>
                        </div>
                        <div class="info-item">
                            <strong>عنوان IP:</strong> ${arduino.ip_address || 'غير محدد'}
                        </div>
                        <div class="info-item">
                            <strong>المنفذ:</strong> ${arduino.port || 'غير محدد'}
                        </div>
                        <div class="info-item">
                            <strong>نوع اللوحة:</strong> ${arduino.board_type || 'غير محدد'}
                        </div>
                        <div class="info-item">
                            <strong>آخر اتصال:</strong> ${arduino.last_seen ? new Date(arduino.last_seen).toLocaleString('ar-SA') : 'لم يتصل بعد'}
                        </div>
                    </div>
                    
                    ${arduino.description ? `<p><strong>الوصف:</strong> ${arduino.description}</p>` : ''}
                    
                    <div style="margin: 15px 0;">
                        <button onclick="pingArduino(${arduino.id})" class="btn btn-warning">فحص الاتصال</button>
                        <button onclick="deleteArduino(${arduino.id})" class="btn btn-danger">حذف</button>
                        <button onclick="toggleCommandSection(${arduino.id})" class="btn">إرسال أمر</button>
                    </div>
                    
                    <div class="command-section" id="commandSection${arduino.id}" style="display: none;">
                        <div class="form-group">
                            <input type="text" id="command${arduino.id}" placeholder="أدخل الأمر" style="margin-bottom: 10px;">
                            <button onclick="sendCommand(${arduino.id})" class="btn">إرسال</button>
                        </div>
                        
                        <div class="command-history" id="commandHistory${arduino.id}">
                            <p>جاري تحميل تاريخ الأوامر...</p>
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        // فحص اتصال جهاز واحد
        async function pingArduino(id) {
            try {
                const response = await fetch(`/api/arduinos/${id}/ping`, {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showAlert(`حالة الجهاز: ${result.status === 'online' ? 'متصل' : 'غير متصل'}`, 'success');
                    refreshArduinos();
                } else {
                    showAlert('خطأ في فحص الاتصال: ' + result.error, 'error');
                }
            } catch (error) {
                showAlert('خطأ في الاتصال: ' + error.message, 'error');
            }
        }
        
        // فحص اتصال جميع الأجهزة
        async function pingAllArduinos() {
            showLoading(true);
            
            for (const arduino of arduinos) {
                await pingArduino(arduino.id);
                await new Promise(resolve => setTimeout(resolve, 500)); // انتظار نصف ثانية بين كل فحص
            }
            
            showLoading(false);
        }
        
        // حذف جهاز
        async function deleteArduino(id) {
            if (!confirm('هل أنت متأكد من حذف هذا الجهاز؟')) {
                return;
            }
            
            try {
                const response = await fetch(`/api/arduinos/${id}`, {
                    method: 'DELETE'
                });
                
                if (response.ok) {
                    showAlert('تم حذف الجهاز بنجاح', 'success');
                    refreshArduinos();
                } else {
                    showAlert('خطأ في حذف الجهاز', 'error');
                }
            } catch (error) {
                showAlert('خطأ في الاتصال: ' + error.message, 'error');
            }
        }
        
        // إظهار/إخفاء قسم الأوامر
        function toggleCommandSection(id) {
            const section = document.getElementById(`commandSection${id}`);
            if (section.style.display === 'none') {
                section.style.display = 'block';
                loadCommandHistory(id);
            } else {
                section.style.display = 'none';
            }
        }
        
        // إرسال أمر لجهاز محدد
        async function sendCommand(id) {
            const commandInput = document.getElementById(`command${id}`);
            const command = commandInput.value.trim();
            
            if (!command) {
                showAlert('يرجى إدخال أمر', 'error');
                return;
            }
            
            try {
                const response = await fetch(`/api/arduinos/${id}/command`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ command: command })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showAlert('تم إرسال الأمر بنجاح', 'success');
                    commandInput.value = '';
                    loadCommandHistory(id);
                    refreshArduinos();
                } else {
                    showAlert('خطأ في إرسال الأمر: ' + result.error, 'error');
                }
            } catch (error) {
                showAlert('خطأ في الاتصال: ' + error.message, 'error');
            }
        }
        
        // تحميل تاريخ الأوامر
        async function loadCommandHistory(id) {
            const historyContainer = document.getElementById(`commandHistory${id}`);
            
            try {
                const response = await fetch(`/api/arduinos/${id}/commands`);
                if (response.ok) {
                    const commands = await response.json();
                    
                    if (commands.length === 0) {
                        historyContainer.innerHTML = '<p>لا توجد أوامر سابقة</p>';
                        return;
                    }
                    
                    historyContainer.innerHTML = commands.map(cmd => `
                        <div class="command-item">
                            <div><strong>الأمر:</strong> ${cmd.command}</div>
                            <div><strong>الحالة:</strong> <span class="command-status status-${cmd.status}">${getStatusText(cmd.status)}</span></div>
                            ${cmd.response ? `<div><strong>الرد:</strong> ${cmd.response}</div>` : ''}
                            <div><strong>التاريخ:</strong> ${new Date(cmd.created_at).toLocaleString('ar-SA')}</div>
                        </div>
                    `).join('');
                } else {
                    historyContainer.innerHTML = '<p>خطأ في تحميل تاريخ الأوامر</p>';
                }
            } catch (error) {
                historyContainer.innerHTML = '<p>خطأ في الاتصال</p>';
            }
        }
        
        // إرسال أمر لجميع الأجهزة
        async function broadcastCommand() {
            const commandInput = document.getElementById('broadcastCommand');
            const command = commandInput.value.trim();
            
            if (!command) {
                showAlert('يرجى إدخال أمر', 'error');
                return;
            }
            
            if (!confirm(`هل أنت متأكد من إرسال الأمر "${command}" لجميع الأجهزة المتصلة؟`)) {
                return;
            }
            
            showLoading(true);
            
            try {
                const response = await fetch('/api/commands/broadcast', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ command: command })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showAlert(`تم إرسال الأمر لـ ${result.total_sent} جهاز`, 'success');
                    commandInput.value = '';
                    refreshArduinos();
                } else {
                    showAlert('خطأ في إرسال الأمر', 'error');
                }
            } catch (error) {
                showAlert('خطأ في الاتصال: ' + error.message, 'error');
            }
            
            showLoading(false);
        }
        
        // دوال مساعدة
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }
        
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            document.querySelector('.main-content').insertBefore(alertDiv, document.querySelector('.section'));
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
        
        function getStatusText(status) {
            const statusMap = {
                'pending': 'في الانتظار',
                'completed': 'مكتمل',
                'failed': 'فشل'
            };
            return statusMap[status] || status;
        }
        
        // إضافة مفتاح Enter لإرسال الأوامر
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                if (e.target.id === 'broadcastCommand') {
                    broadcastCommand();
                } else if (e.target.id.startsWith('command')) {
                    const id = e.target.id.replace('command', '');
                    sendCommand(parseInt(id));
                }
            }
        });
    </script>
</body>
</html>

