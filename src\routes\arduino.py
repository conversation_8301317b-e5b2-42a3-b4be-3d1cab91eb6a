from flask import Blueprint, request, jsonify
from src.models.arduino import db, Arduino, ArduinoCommand
from datetime import datetime
import requests
import socket

arduino_bp = Blueprint('arduino', __name__)

@arduino_bp.route('/arduinos', methods=['GET'])
def get_arduinos():
    """Get all Arduino devices"""
    arduinos = Arduino.query.all()
    return jsonify([arduino.to_dict() for arduino in arduinos])

@arduino_bp.route('/arduinos', methods=['POST'])
def add_arduino():
    """Add a new Arduino device"""
    data = request.get_json()
    
    if not data or 'name' not in data:
        return jsonify({'error': 'Name is required'}), 400
    
    arduino = Arduino(
        name=data['name'],
        ip_address=data.get('ip_address'),
        port=data.get('port', 80),
        board_type=data.get('board_type'),
        description=data.get('description')
    )
    
    db.session.add(arduino)
    db.session.commit()
    
    return jsonify(arduino.to_dict()), 201

@arduino_bp.route('/arduinos/<int:arduino_id>', methods=['PUT'])
def update_arduino(arduino_id):
    """Update Arduino device"""
    arduino = Arduino.query.get_or_404(arduino_id)
    data = request.get_json()
    
    if 'name' in data:
        arduino.name = data['name']
    if 'ip_address' in data:
        arduino.ip_address = data['ip_address']
    if 'port' in data:
        arduino.port = data['port']
    if 'board_type' in data:
        arduino.board_type = data['board_type']
    if 'description' in data:
        arduino.description = data['description']
    
    db.session.commit()
    return jsonify(arduino.to_dict())

@arduino_bp.route('/arduinos/<int:arduino_id>', methods=['DELETE'])
def delete_arduino(arduino_id):
    """Delete Arduino device"""
    arduino = Arduino.query.get_or_404(arduino_id)
    db.session.delete(arduino)
    db.session.commit()
    return '', 204

@arduino_bp.route('/arduinos/<int:arduino_id>/ping', methods=['POST'])
def ping_arduino(arduino_id):
    """Ping Arduino to check connectivity"""
    arduino = Arduino.query.get_or_404(arduino_id)
    
    if not arduino.ip_address:
        return jsonify({'error': 'No IP address configured'}), 400
    
    try:
        # Try to connect to the Arduino
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((arduino.ip_address, arduino.port or 80))
        sock.close()
        
        if result == 0:
            arduino.status = 'online'
            arduino.last_seen = datetime.utcnow()
        else:
            arduino.status = 'offline'
        
        db.session.commit()
        return jsonify({'status': arduino.status, 'last_seen': arduino.last_seen.isoformat() if arduino.last_seen else None})
        
    except Exception as e:
        arduino.status = 'offline'
        db.session.commit()
        return jsonify({'error': str(e), 'status': 'offline'}), 500

@arduino_bp.route('/arduinos/<int:arduino_id>/command', methods=['POST'])
def send_command(arduino_id):
    """Send command to Arduino"""
    arduino = Arduino.query.get_or_404(arduino_id)
    data = request.get_json()
    
    if not data or 'command' not in data:
        return jsonify({'error': 'Command is required'}), 400
    
    command_record = ArduinoCommand(
        arduino_id=arduino_id,
        command=data['command']
    )
    
    db.session.add(command_record)
    db.session.commit()
    
    # Try to send command to Arduino
    if arduino.ip_address:
        try:
            # This is a simple HTTP request - Arduino should have a web server
            url = f"http://{arduino.ip_address}:{arduino.port or 80}/command"
            response = requests.post(url, json={'command': data['command']}, timeout=10)
            
            command_record.response = response.text
            command_record.status = 'completed' if response.status_code == 200 else 'failed'
            command_record.executed_at = datetime.utcnow()
            
            arduino.last_seen = datetime.utcnow()
            arduino.status = 'online'
            
        except Exception as e:
            command_record.response = str(e)
            command_record.status = 'failed'
            command_record.executed_at = datetime.utcnow()
            arduino.status = 'offline'
    else:
        command_record.status = 'failed'
        command_record.response = 'No IP address configured'
    
    db.session.commit()
    return jsonify(command_record.to_dict())

@arduino_bp.route('/arduinos/<int:arduino_id>/commands', methods=['GET'])
def get_arduino_commands(arduino_id):
    """Get command history for Arduino"""
    commands = ArduinoCommand.query.filter_by(arduino_id=arduino_id).order_by(ArduinoCommand.created_at.desc()).all()
    return jsonify([cmd.to_dict() for cmd in commands])

@arduino_bp.route('/commands/broadcast', methods=['POST'])
def broadcast_command():
    """Send command to all online Arduino devices"""
    data = request.get_json()
    
    if not data or 'command' not in data:
        return jsonify({'error': 'Command is required'}), 400
    
    arduinos = Arduino.query.filter_by(status='online').all()
    results = []
    
    for arduino in arduinos:
        command_record = ArduinoCommand(
            arduino_id=arduino.id,
            command=data['command']
        )
        
        db.session.add(command_record)
        
        if arduino.ip_address:
            try:
                url = f"http://{arduino.ip_address}:{arduino.port or 80}/command"
                response = requests.post(url, json={'command': data['command']}, timeout=10)
                
                command_record.response = response.text
                command_record.status = 'completed' if response.status_code == 200 else 'failed'
                command_record.executed_at = datetime.utcnow()
                
                arduino.last_seen = datetime.utcnow()
                
            except Exception as e:
                command_record.response = str(e)
                command_record.status = 'failed'
                command_record.executed_at = datetime.utcnow()
                arduino.status = 'offline'
        
        results.append({
            'arduino_id': arduino.id,
            'arduino_name': arduino.name,
            'command_id': command_record.id,
            'status': command_record.status
        })
    
    db.session.commit()
    return jsonify({'results': results, 'total_sent': len(results)})

