from flask_sqlalchemy import SQLAlchemy
from datetime import datetime

db = SQLAlchemy()

class Arduino(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    ip_address = db.Column(db.String(15), nullable=True)
    port = db.Column(db.Integer, nullable=True)
    status = db.Column(db.String(20), default='offline')
    board_type = db.Column(db.String(50), nullable=True)
    description = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_seen = db.Column(db.DateTime, nullable=True)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'ip_address': self.ip_address,
            'port': self.port,
            'status': self.status,
            'board_type': self.board_type,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_seen': self.last_seen.isoformat() if self.last_seen else None
        }

class ArduinoCommand(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    arduino_id = db.Column(db.Integer, db.ForeignKey('arduino.id'), nullable=False)
    command = db.Column(db.Text, nullable=False)
    response = db.Column(db.Text, nullable=True)
    status = db.Column(db.String(20), default='pending')  # pending, sent, completed, failed
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    executed_at = db.Column(db.DateTime, nullable=True)
    
    arduino = db.relationship('Arduino', backref=db.backref('commands', lazy=True))
    
    def to_dict(self):
        return {
            'id': self.id,
            'arduino_id': self.arduino_id,
            'command': self.command,
            'response': self.response,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'executed_at': self.executed_at.isoformat() if self.executed_at else None
        }

