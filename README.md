# مدير Arduino - أداة إدارة عدة أجهزة Arduino

## نظرة عامة

مدير Arduino هو تطبيق ويب متقدم يتيح إدارة والتحكم في عدة أجهزة Arduino من واجهة واحدة. التطبيق مبني باستخدام Flask (Python) في الواجهة الخلفية وHTML/CSS/JavaScript في الواجهة الأمامية، مع دعم كامل للغة العربية واتجاه RTL.

## الميزات الرئيسية

- ✅ إدارة عدد غير محدود من أجهزة Arduino
- ✅ فحص اتصال الأجهزة في الوقت الفعلي
- ✅ إرسال أوامر فردية أو جماعية
- ✅ تتبع تاريخ الأوامر والاستجابات
- ✅ واجهة مستخدم عربية متجاوبة
- ✅ دعم أنواع مختلفة من لوحات Arduino
- ✅ نظام تنبيهات تفاعلي
- ✅ قاعدة بيانات SQLite مدمجة

## متطلبات النظام

- Python 3.8 أو أحدث
- متصفح ويب حديث
- اتصال إنترنت (للمكتبات والتحديثات)

## التثبيت والتشغيل

### 1. إعداد البيئة

```bash
# إنشاء بيئة افتراضية
python -m venv venv

# تفعيل البيئة الافتراضية
# على Windows:
venv\Scripts\activate
# على Linux/Mac:
source venv/bin/activate

# تثبيت المتطلبات
pip install -r requirements.txt
```

### 2. تشغيل التطبيق

```bash
# تشغيل الخادم
python run_app.py
```

### 3. الوصول إلى التطبيق

افتح المتصفح وانتقل إلى: `http://localhost:8080`

## هيكل المشروع

```
arduino-manager/
├── src/
│   ├── models/
│   │   ├── arduino.py      # نماذج قاعدة البيانات
│   │   └── user.py         # نموذج المستخدم
│   ├── routes/
│   │   ├── arduino.py      # مسارات API للـ Arduino
│   │   └── user.py         # مسارات المستخدم
│   ├── static/
│   │   └── index.html      # واجهة المستخدم
│   ├── database/
│   │   └── app.db          # قاعدة البيانات
│   └── main.py             # التطبيق الرئيسي
├── venv/                   # البيئة الافتراضية
├── requirements.txt        # متطلبات Python
├── run_app.py             # ملف تشغيل التطبيق
└── README.md              # هذا الملف
```

## واجهات برمجة التطبيقات (APIs)

### إدارة الأجهزة

- `GET /api/arduinos` - استرجاع قائمة الأجهزة
- `POST /api/arduinos` - إضافة جهاز جديد
- `PUT /api/arduinos/{id}` - تحديث معلومات جهاز
- `DELETE /api/arduinos/{id}` - حذف جهاز

### التحكم والمراقبة

- `POST /api/arduinos/{id}/ping` - فحص اتصال جهاز
- `POST /api/arduinos/{id}/command` - إرسال أمر لجهاز
- `GET /api/arduinos/{id}/commands` - استرجاع تاريخ الأوامر
- `POST /api/commands/broadcast` - إرسال أمر لجميع الأجهزة

## إعداد Arduino

لكي يعمل جهاز Arduino مع هذه الأداة، يجب أن يحتوي على:

1. **اتصال WiFi** (ESP32, ESP8266, أو Arduino مع WiFi Shield)
2. **خادم ويب** يستقبل طلبات HTTP POST
3. **نقطة نهاية** `/command` لاستقبال الأوامر

### مثال على كود Arduino

```cpp
#include <WiFi.h>
#include <WebServer.h>
#include <ArduinoJson.h>

const char* ssid = "YOUR_WIFI_SSID";
const char* password = "YOUR_WIFI_PASSWORD";

WebServer server(80);

void setup() {
  Serial.begin(115200);
  
  WiFi.begin(ssid, password);
  while (WiFi.status() != WL_CONNECTED) {
    delay(1000);
    Serial.println("Connecting to WiFi...");
  }
  
  Serial.println("WiFi connected!");
  Serial.print("IP address: ");
  Serial.println(WiFi.localIP());
  
  server.on("/command", HTTP_POST, handleCommand);
  server.begin();
}

void loop() {
  server.handleClient();
}

void handleCommand() {
  if (server.hasArg("plain")) {
    String body = server.arg("plain");
    DynamicJsonDocument doc(1024);
    deserializeJson(doc, body);
    
    String command = doc["command"];
    String response = executeCommand(command);
    
    server.send(200, "text/plain", response);
  } else {
    server.send(400, "text/plain", "No command received");
  }
}

String executeCommand(String command) {
  if (command == "LED_ON") {
    digitalWrite(LED_BUILTIN, HIGH);
    return "LED turned ON";
  } else if (command == "LED_OFF") {
    digitalWrite(LED_BUILTIN, LOW);
    return "LED turned OFF";
  } else if (command == "STATUS") {
    return "Arduino is running normally";
  } else {
    return "Unknown command: " + command;
  }
}
```

## الاستخدام

### إضافة جهاز Arduino جديد

1. املأ نموذج "إضافة Arduino جديد"
2. أدخل اسم الجهاز (مطلوب)
3. أدخل عنوان IP (اختياري)
4. حدد المنفذ (افتراضي: 80)
5. اختر نوع اللوحة
6. أضف وصفاً للجهاز
7. اضغط "إضافة Arduino"

### إرسال الأوامر

#### أمر فردي:
1. اضغط "إرسال أمر" بجانب الجهاز المطلوب
2. أدخل الأمر في الحقل المخصص
3. اضغط "إرسال"

#### أمر جماعي:
1. أدخل الأمر في حقل "إرسال أمر لجميع الأجهزة المتصلة"
2. اضغط "إرسال للجميع"

### فحص الاتصال

- **جهاز واحد:** اضغط "فحص الاتصال" بجانب الجهاز
- **جميع الأجهزة:** اضغط "فحص الاتصال للجميع"

## استكشاف الأخطاء

### مشاكل شائعة

1. **عدم الاتصال بـ Arduino:**
   - تحقق من عنوان IP
   - تأكد من اتصال Arduino بالشبكة
   - فحص إعدادات جدار الحماية

2. **بطء في الاستجابة:**
   - تحقق من قوة إشارة WiFi
   - قلل من تكرار الطلبات
   - تحسين كود Arduino

3. **أخطاء في قاعدة البيانات:**
   - إعادة تشغيل التطبيق
   - فحص صلاحيات الملفات
   - التأكد من مساحة التخزين

## التطوير والمساهمة

### إضافة ميزات جديدة

1. Fork المشروع
2. أنشئ branch جديد للميزة
3. اكتب الكود واختبره
4. أرسل Pull Request

### هيكل الكود

- **النماذج:** في مجلد `src/models/`
- **المسارات:** في مجلد `src/routes/`
- **الواجهة:** في مجلد `src/static/`

## الأمان

- استخدم HTTPS في البيئة الإنتاجية
- فعّل المصادقة للوصول الخارجي
- حدّث كلمات المرور بانتظام
- راقب سجلات النظام

## الترخيص

هذا المشروع متاح تحت رخصة MIT. يمكن استخدامه وتعديله ومشاركته بحرية.

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- أنشئ Issue في GitHub
- راجع الدليل الشامل المرفق
- تواصل مع مجتمع Arduino

## الإصدارات

- **v1.0.0** - الإصدار الأولي مع الميزات الأساسية
- المزيد من الميزات قادمة في الإصدارات القادمة

---

**تم تطوير هذا المشروع بواسطة Manus AI**

